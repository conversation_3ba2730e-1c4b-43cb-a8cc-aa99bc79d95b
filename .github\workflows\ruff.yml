name: Ruff

on:
  push:
    branches:
      - main
      - dev
      - dev-refactor # 例如：匹配所有以 feature/ 开头的分支
      # 添加你希望触发此 workflow 的其他分支

permissions:
  contents: write

jobs:
  ruff:
    runs-on: ubuntu-latest
    # 关键修改：添加条件判断
    # 确保只有在 event_name 是 'push' 且不是由 Pull Request 引起的 push 时才运行
    if: github.event_name == 'push' && !startsWith(github.ref, 'refs/pull/')

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref || github.ref_name }}
      - name: Install the latest version of ruff
        uses: astral-sh/ruff-action@v3
        with:
          version: "latest"
      - run: ruff check --fix
      - run: ruff format
      - name: Commit changes
        if: success()
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add -A
          git diff --quiet && git diff --staged --quiet || git commit -m "🤖 自动格式化代码 [skip ci]"
          git push